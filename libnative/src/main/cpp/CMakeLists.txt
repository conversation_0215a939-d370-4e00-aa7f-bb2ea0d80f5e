
cmake_minimum_required(VERSION 3.10.2)


project("nativelib")

# lame
include_directories(${CMAKE_SOURCE_DIR}/module/mp3/lame)
aux_source_directory(${CMAKE_SOURCE_DIR}/module/mp3/lame SRC_LAME)

set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -include string.h -include stdlib.h")
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -include string.h -include stdlib.h")

add_library(
        nativelib
        SHARED

        ${SRC_LAME}
        utils/logger.cpp
        module/yuv/yuv.cpp
        module/mp3/mp3.cpp
        proxy/proxy_yuv.cpp
        proxy/proxy_mp3.cpp
        nativelib.cpp)

# 為 nativelib 目標新增 include 路徑
target_include_directories(nativelib
        PRIVATE
        ${CMAKE_CURRENT_SOURCE_DIR}/module/mp3/lame
)

# 關鍵步驟：為所有原始檔新增全域的編譯定義
# 這會在編譯時，自動在每個檔案的開頭加上這兩個 include，解決所有錯誤
#target_compile_options(nativelib
#        PRIVATE
#        -include "string.h"
#        -include "stdlib.h"
#)

find_library(
        log-lib
        log)

target_link_libraries(
        nativelib
        android
        ${log-lib})