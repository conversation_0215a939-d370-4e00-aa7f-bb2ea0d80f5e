ninja: Entering directory `/Users/<USER>/AndroidStudioProjects/UVCCam/libnative/.cxx/RelWithDebInfo/4u6a1j5c/arm64-v8a'
[1/27] Building C object CMakeFiles/nativelib.dir/module/mp3/lame/mpglib_interface.c.o
[2/27] Building C object CMakeFiles/nativelib.dir/module/mp3/lame/fft.c.o
[3/27] Building C object CMakeFiles/nativelib.dir/module/mp3/lame/presets.c.o
/Users/<USER>/AndroidStudioProjects/UVCCam/libnative/src/main/cpp/module/mp3/lame/presets.c:170:5: warning: using floating point absolute value function 'fabs' when argument is of integer type [-Wabsolute-value]
  170 |     SET_OPTION(quant_comp, set->quant_comp, -1);
      |     ^
/Users/<USER>/AndroidStudioProjects/UVCCam/libnative/src/main/cpp/module/mp3/lame/presets.c:36:16: note: expanded from macro 'SET_OPTION'
   36 |     else if (!(fabs(lame_get_##opt(gfp) - def) > 0)) \
      |                ^
/Users/<USER>/AndroidStudioProjects/UVCCam/libnative/src/main/cpp/module/mp3/lame/presets.c:170:5: note: use function 'abs' instead
/Users/<USER>/AndroidStudioProjects/UVCCam/libnative/src/main/cpp/module/mp3/lame/presets.c:36:16: note: expanded from macro 'SET_OPTION'
   36 |     else if (!(fabs(lame_get_##opt(gfp) - def) > 0)) \
      |                ^
/Users/<USER>/AndroidStudioProjects/UVCCam/libnative/src/main/cpp/module/mp3/lame/presets.c:171:5: warning: using floating point absolute value function 'fabs' when argument is of integer type [-Wabsolute-value]
  171 |     SET_OPTION(quant_comp_short, set->quant_comp_s, -1);
      |     ^
/Users/<USER>/AndroidStudioProjects/UVCCam/libnative/src/main/cpp/module/mp3/lame/presets.c:36:16: note: expanded from macro 'SET_OPTION'
   36 |     else if (!(fabs(lame_get_##opt(gfp) - def) > 0)) \
      |                ^
/Users/<USER>/AndroidStudioProjects/UVCCam/libnative/src/main/cpp/module/mp3/lame/presets.c:171:5: note: use function 'abs' instead
/Users/<USER>/AndroidStudioProjects/UVCCam/libnative/src/main/cpp/module/mp3/lame/presets.c:36:16: note: expanded from macro 'SET_OPTION'
   36 |     else if (!(fabs(lame_get_##opt(gfp) - def) > 0)) \
      |                ^
/Users/<USER>/AndroidStudioProjects/UVCCam/libnative/src/main/cpp/module/mp3/lame/presets.c:280:5: warning: using floating point absolute value function 'fabs' when argument is of integer type [-Wabsolute-value]
  280 |     SET_OPTION(quant_comp, abr_switch_map[r].quant_comp, -1);
      |     ^
/Users/<USER>/AndroidStudioProjects/UVCCam/libnative/src/main/cpp/module/mp3/lame/presets.c:36:16: note: expanded from macro 'SET_OPTION'
   36 |     else if (!(fabs(lame_get_##opt(gfp) - def) > 0)) \
      |                ^
/Users/<USER>/AndroidStudioProjects/UVCCam/libnative/src/main/cpp/module/mp3/lame/presets.c:280:5: note: use function 'abs' instead
/Users/<USER>/AndroidStudioProjects/UVCCam/libnative/src/main/cpp/module/mp3/lame/presets.c:36:16: note: expanded from macro 'SET_OPTION'
   36 |     else if (!(fabs(lame_get_##opt(gfp) - def) > 0)) \
      |                ^
/Users/<USER>/AndroidStudioProjects/UVCCam/libnative/src/main/cpp/module/mp3/lame/presets.c:281:5: warning: using floating point absolute value function 'fabs' when argument is of integer type [-Wabsolute-value]
  281 |     SET_OPTION(quant_comp_short, abr_switch_map[r].quant_comp_s, -1);
      |     ^
/Users/<USER>/AndroidStudioProjects/UVCCam/libnative/src/main/cpp/module/mp3/lame/presets.c:36:16: note: expanded from macro 'SET_OPTION'
   36 |     else if (!(fabs(lame_get_##opt(gfp) - def) > 0)) \
      |                ^
/Users/<USER>/AndroidStudioProjects/UVCCam/libnative/src/main/cpp/module/mp3/lame/presets.c:281:5: note: use function 'abs' instead
/Users/<USER>/AndroidStudioProjects/UVCCam/libnative/src/main/cpp/module/mp3/lame/presets.c:36:16: note: expanded from macro 'SET_OPTION'
   36 |     else if (!(fabs(lame_get_##opt(gfp) - def) > 0)) \
      |                ^
4 warnings generated.
[4/27] Building C object CMakeFiles/nativelib.dir/module/mp3/lame/gain_analysis.c.o
[5/27] Building C object CMakeFiles/nativelib.dir/module/mp3/lame/encoder.c.o
[6/27] Building C object CMakeFiles/nativelib.dir/module/mp3/lame/reservoir.c.o
[7/27] Building C object CMakeFiles/nativelib.dir/module/mp3/lame/VbrTag.c.o
/Users/<USER>/AndroidStudioProjects/UVCCam/libnative/src/main/cpp/module/mp3/lame/VbrTag.c:264:5: warning: shifting a negative signed value is undefined [-Wshift-negative-value]
  264 |     SHIFT_IN_BITS_VALUE(buffer[0], 8u, 0xffu);
      |     ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Users/<USER>/AndroidStudioProjects/UVCCam/libnative/src/main/cpp/module/mp3/lame/VbrTag.c:255:68: note: expanded from macro 'SHIFT_IN_BITS_VALUE'
  255 | #define SHIFT_IN_BITS_VALUE(x,n,v) ( x = (x << (n)) | ( (v) & ~(-1 << (n)) ) )
      |                                                                 ~~ ^
/Users/<USER>/AndroidStudioProjects/UVCCam/libnative/src/main/cpp/module/mp3/lame/VbrTag.c:266:5: warning: shifting a negative signed value is undefined [-Wshift-negative-value]
  266 |     SHIFT_IN_BITS_VALUE(buffer[1], 3u, 7);
      |     ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Users/<USER>/AndroidStudioProjects/UVCCam/libnative/src/main/cpp/module/mp3/lame/VbrTag.c:255:68: note: expanded from macro 'SHIFT_IN_BITS_VALUE'
  255 | #define SHIFT_IN_BITS_VALUE(x,n,v) ( x = (x << (n)) | ( (v) & ~(-1 << (n)) ) )
      |                                                                 ~~ ^
/Users/<USER>/AndroidStudioProjects/UVCCam/libnative/src/main/cpp/module/mp3/lame/VbrTag.c:267:5: warning: shifting a negative signed value is undefined [-Wshift-negative-value]
  267 |     SHIFT_IN_BITS_VALUE(buffer[1], 1u, (cfg->samplerate_out < 16000) ? 0 : 1);
      |     ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Users/<USER>/AndroidStudioProjects/UVCCam/libnative/src/main/cpp/module/mp3/lame/VbrTag.c:255:68: note: expanded from macro 'SHIFT_IN_BITS_VALUE'
  255 | #define SHIFT_IN_BITS_VALUE(x,n,v) ( x = (x << (n)) | ( (v) & ~(-1 << (n)) ) )
      |                                                                 ~~ ^
/Users/<USER>/AndroidStudioProjects/UVCCam/libnative/src/main/cpp/module/mp3/lame/VbrTag.c:268:5: warning: shifting a negative signed value is undefined [-Wshift-negative-value]
  268 |     SHIFT_IN_BITS_VALUE(buffer[1], 1u, cfg->version);
      |     ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Users/<USER>/AndroidStudioProjects/UVCCam/libnative/src/main/cpp/module/mp3/lame/VbrTag.c:255:68: note: expanded from macro 'SHIFT_IN_BITS_VALUE'
  255 | #define SHIFT_IN_BITS_VALUE(x,n,v) ( x = (x << (n)) | ( (v) & ~(-1 << (n)) ) )
      |                                                                 ~~ ^
/Users/<USER>/AndroidStudioProjects/UVCCam/libnative/src/main/cpp/module/mp3/lame/VbrTag.c:269:5: warning: shifting a negative signed value is undefined [-Wshift-negative-value]
  269 |     SHIFT_IN_BITS_VALUE(buffer[1], 2u, 4 - 3);
      |     ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Users/<USER>/AndroidStudioProjects/UVCCam/libnative/src/main/cpp/module/mp3/lame/VbrTag.c:255:68: note: expanded from macro 'SHIFT_IN_BITS_VALUE'
  255 | #define SHIFT_IN_BITS_VALUE(x,n,v) ( x = (x << (n)) | ( (v) & ~(-1 << (n)) ) )
      |                                                                 ~~ ^
/Users/<USER>/AndroidStudioProjects/UVCCam/libnative/src/main/cpp/module/mp3/lame/VbrTag.c:270:5: warning: shifting a negative signed value is undefined [-Wshift-negative-value]
  270 |     SHIFT_IN_BITS_VALUE(buffer[1], 1u, (!cfg->error_protection) ? 1 : 0);
      |     ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Users/<USER>/AndroidStudioProjects/UVCCam/libnative/src/main/cpp/module/mp3/lame/VbrTag.c:255:68: note: expanded from macro 'SHIFT_IN_BITS_VALUE'
  255 | #define SHIFT_IN_BITS_VALUE(x,n,v) ( x = (x << (n)) | ( (v) & ~(-1 << (n)) ) )
      |                                                                 ~~ ^
/Users/<USER>/AndroidStudioProjects/UVCCam/libnative/src/main/cpp/module/mp3/lame/VbrTag.c:272:5: warning: shifting a negative signed value is undefined [-Wshift-negative-value]
  272 |     SHIFT_IN_BITS_VALUE(buffer[2], 4u, eov->bitrate_index);
      |     ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Users/<USER>/AndroidStudioProjects/UVCCam/libnative/src/main/cpp/module/mp3/lame/VbrTag.c:255:68: note: expanded from macro 'SHIFT_IN_BITS_VALUE'
  255 | #define SHIFT_IN_BITS_VALUE(x,n,v) ( x = (x << (n)) | ( (v) & ~(-1 << (n)) ) )
      |                                                                 ~~ ^
/Users/<USER>/AndroidStudioProjects/UVCCam/libnative/src/main/cpp/module/mp3/lame/VbrTag.c:273:5: warning: shifting a negative signed value is undefined [-Wshift-negative-value]
  273 |     SHIFT_IN_BITS_VALUE(buffer[2], 2u, cfg->samplerate_index);
      |     ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Users/<USER>/AndroidStudioProjects/UVCCam/libnative/src/main/cpp/module/mp3/lame/VbrTag.c:255:68: note: expanded from macro 'SHIFT_IN_BITS_VALUE'
  255 | #define SHIFT_IN_BITS_VALUE(x,n,v) ( x = (x << (n)) | ( (v) & ~(-1 << (n)) ) )
      |                                                                 ~~ ^
/Users/<USER>/AndroidStudioProjects/UVCCam/libnative/src/main/cpp/module/mp3/lame/VbrTag.c:274:5: warning: shifting a negative signed value is undefined [-Wshift-negative-value]
  274 |     SHIFT_IN_BITS_VALUE(buffer[2], 1u, 0);
      |     ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Users/<USER>/AndroidStudioProjects/UVCCam/libnative/src/main/cpp/module/mp3/lame/VbrTag.c:255:68: note: expanded from macro 'SHIFT_IN_BITS_VALUE'
  255 | #define SHIFT_IN_BITS_VALUE(x,n,v) ( x = (x << (n)) | ( (v) & ~(-1 << (n)) ) )
      |                                                                 ~~ ^
/Users/<USER>/AndroidStudioProjects/UVCCam/libnative/src/main/cpp/module/mp3/lame/VbrTag.c:275:5: warning: shifting a negative signed value is undefined [-Wshift-negative-value]
  275 |     SHIFT_IN_BITS_VALUE(buffer[2], 1u, cfg->extension);
      |     ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Users/<USER>/AndroidStudioProjects/UVCCam/libnative/src/main/cpp/module/mp3/lame/VbrTag.c:255:68: note: expanded from macro 'SHIFT_IN_BITS_VALUE'
  255 | #define SHIFT_IN_BITS_VALUE(x,n,v) ( x = (x << (n)) | ( (v) & ~(-1 << (n)) ) )
      |                                                                 ~~ ^
/Users/<USER>/AndroidStudioProjects/UVCCam/libnative/src/main/cpp/module/mp3/lame/VbrTag.c:277:5: warning: shifting a negative signed value is undefined [-Wshift-negative-value]
  277 |     SHIFT_IN_BITS_VALUE(buffer[3], 2u, cfg->mode);
      |     ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Users/<USER>/AndroidStudioProjects/UVCCam/libnative/src/main/cpp/module/mp3/lame/VbrTag.c:255:68: note: expanded from macro 'SHIFT_IN_BITS_VALUE'
  255 | #define SHIFT_IN_BITS_VALUE(x,n,v) ( x = (x << (n)) | ( (v) & ~(-1 << (n)) ) )
      |                                                                 ~~ ^
/Users/<USER>/AndroidStudioProjects/UVCCam/libnative/src/main/cpp/module/mp3/lame/VbrTag.c:278:5: warning: shifting a negative signed value is undefined [-Wshift-negative-value]
  278 |     SHIFT_IN_BITS_VALUE(buffer[3], 2u, eov->mode_ext);
      |     ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Users/<USER>/AndroidStudioProjects/UVCCam/libnative/src/main/cpp/module/mp3/lame/VbrTag.c:255:68: note: expanded from macro 'SHIFT_IN_BITS_VALUE'
  255 | #define SHIFT_IN_BITS_VALUE(x,n,v) ( x = (x << (n)) | ( (v) & ~(-1 << (n)) ) )
      |                                                                 ~~ ^
/Users/<USER>/AndroidStudioProjects/UVCCam/libnative/src/main/cpp/module/mp3/lame/VbrTag.c:279:5: warning: shifting a negative signed value is undefined [-Wshift-negative-value]
  279 |     SHIFT_IN_BITS_VALUE(buffer[3], 1u, cfg->copyright);
      |     ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Users/<USER>/AndroidStudioProjects/UVCCam/libnative/src/main/cpp/module/mp3/lame/VbrTag.c:255:68: note: expanded from macro 'SHIFT_IN_BITS_VALUE'
  255 | #define SHIFT_IN_BITS_VALUE(x,n,v) ( x = (x << (n)) | ( (v) & ~(-1 << (n)) ) )
      |                                                                 ~~ ^
/Users/<USER>/AndroidStudioProjects/UVCCam/libnative/src/main/cpp/module/mp3/lame/VbrTag.c:280:5: warning: shifting a negative signed value is undefined [-Wshift-negative-value]
  280 |     SHIFT_IN_BITS_VALUE(buffer[3], 1u, cfg->original);
      |     ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Users/<USER>/AndroidStudioProjects/UVCCam/libnative/src/main/cpp/module/mp3/lame/VbrTag.c:255:68: note: expanded from macro 'SHIFT_IN_BITS_VALUE'
  255 | #define SHIFT_IN_BITS_VALUE(x,n,v) ( x = (x << (n)) | ( (v) & ~(-1 << (n)) ) )
      |                                                                 ~~ ^
/Users/<USER>/AndroidStudioProjects/UVCCam/libnative/src/main/cpp/module/mp3/lame/VbrTag.c:281:5: warning: shifting a negative signed value is undefined [-Wshift-negative-value]
  281 |     SHIFT_IN_BITS_VALUE(buffer[3], 2u, cfg->emphasis);
      |     ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Users/<USER>/AndroidStudioProjects/UVCCam/libnative/src/main/cpp/module/mp3/lame/VbrTag.c:255:68: note: expanded from macro 'SHIFT_IN_BITS_VALUE'
  255 | #define SHIFT_IN_BITS_VALUE(x,n,v) ( x = (x << (n)) | ( (v) & ~(-1 << (n)) ) )
      |                                                                 ~~ ^
/Users/<USER>/AndroidStudioProjects/UVCCam/libnative/src/main/cpp/module/mp3/lame/VbrTag.c:431:23: warning: comparison of array 'pTagData->toc' not equal to a null pointer is always true [-Wtautological-pointer-compare]
  431 |         if (pTagData->toc != NULL) {
      |             ~~~~~~~~~~^~~    ~~~~
16 warnings generated.
[8/27] Building C object CMakeFiles/nativelib.dir/module/mp3/lame/tables.c.o
[9/27] Building C object CMakeFiles/nativelib.dir/module/mp3/lame/version.c.o
[10/27] Building C object CMakeFiles/nativelib.dir/module/mp3/lame/newmdct.c.o
[11/27] Building CXX object CMakeFiles/nativelib.dir/utils/logger.cpp.o
[12/27] Building C object CMakeFiles/nativelib.dir/module/mp3/lame/util.c.o
[13/27] Building CXX object CMakeFiles/nativelib.dir/module/yuv/yuv.cpp.o
[14/27] Building CXX object CMakeFiles/nativelib.dir/module/mp3/mp3.cpp.o
[15/27] Building C object CMakeFiles/nativelib.dir/module/mp3/lame/set_get.c.o
[16/27] Building CXX object CMakeFiles/nativelib.dir/proxy/proxy_mp3.cpp.o
[17/27] Building C object CMakeFiles/nativelib.dir/module/mp3/lame/lame.c.o
/Users/<USER>/AndroidStudioProjects/UVCCam/libnative/src/main/cpp/module/mp3/lame/lame.c:628:13: warning: using floating point absolute value function 'fabs' when argument is of integer type [-Wabsolute-value]
  628 |         if (EQ(gfp->compression_ratio, 0))
      |             ^
/Users/<USER>/AndroidStudioProjects/UVCCam/libnative/src/main/cpp/module/mp3/lame/machine.h:168:12: note: expanded from macro 'EQ'
  168 | (fabs(a) > fabs(b)) \
      |            ^
/Users/<USER>/AndroidStudioProjects/UVCCam/libnative/src/main/cpp/module/mp3/lame/lame.c:628:13: note: use function 'abs' instead
/Users/<USER>/AndroidStudioProjects/UVCCam/libnative/src/main/cpp/module/mp3/lame/machine.h:168:12: note: expanded from macro 'EQ'
  168 | (fabs(a) > fabs(b)) \
      |            ^
/Users/<USER>/AndroidStudioProjects/UVCCam/libnative/src/main/cpp/module/mp3/lame/lame.c:628:13: warning: using floating point absolute value function 'fabs' when argument is of integer type [-Wabsolute-value]
  628 |         if (EQ(gfp->compression_ratio, 0))
      |             ^
/Users/<USER>/AndroidStudioProjects/UVCCam/libnative/src/main/cpp/module/mp3/lame/machine.h:170:23: note: expanded from macro 'EQ'
  170 |  : (fabs((a)-(b)) <= (fabs(b) * 1e-6f)))
      |                       ^
/Users/<USER>/AndroidStudioProjects/UVCCam/libnative/src/main/cpp/module/mp3/lame/lame.c:628:13: note: use function 'abs' instead
/Users/<USER>/AndroidStudioProjects/UVCCam/libnative/src/main/cpp/module/mp3/lame/machine.h:170:23: note: expanded from macro 'EQ'
  170 |  : (fabs((a)-(b)) <= (fabs(b) * 1e-6f)))
      |                       ^
/Users/<USER>/AndroidStudioProjects/UVCCam/libnative/src/main/cpp/module/mp3/lame/lame.c:1596:13: warning: using floating point absolute value function 'fabs' when argument is of integer type [-Wabsolute-value]
 1596 |         if (NEQ(RadioGain, GAIN_NOT_ENOUGH_SAMPLES)) {
      |             ^
/Users/<USER>/AndroidStudioProjects/UVCCam/libnative/src/main/cpp/module/mp3/lame/machine.h:175:20: note: expanded from macro 'NEQ'
  175 | #define NEQ(a,b) (!EQ(a,b))
      |                    ^
/Users/<USER>/AndroidStudioProjects/UVCCam/libnative/src/main/cpp/module/mp3/lame/machine.h:168:12: note: expanded from macro 'EQ'
  168 | (fabs(a) > fabs(b)) \
      |            ^
/Users/<USER>/AndroidStudioProjects/UVCCam/libnative/src/main/cpp/module/mp3/lame/lame.c:1596:13: note: use function 'abs' instead
/Users/<USER>/AndroidStudioProjects/UVCCam/libnative/src/main/cpp/module/mp3/lame/machine.h:175:20: note: expanded from macro 'NEQ'
  175 | #define NEQ(a,b) (!EQ(a,b))
      |                    ^
/Users/<USER>/AndroidStudioProjects/UVCCam/libnative/src/main/cpp/module/mp3/lame/machine.h:168:12: note: expanded from macro 'EQ'
  168 | (fabs(a) > fabs(b)) \
      |            ^
/Users/<USER>/AndroidStudioProjects/UVCCam/libnative/src/main/cpp/module/mp3/lame/lame.c:1596:13: warning: using floating point absolute value function 'fabs' when argument is of integer type [-Wabsolute-value]
 1596 |         if (NEQ(RadioGain, GAIN_NOT_ENOUGH_SAMPLES)) {
      |             ^
/Users/<USER>/AndroidStudioProjects/UVCCam/libnative/src/main/cpp/module/mp3/lame/machine.h:175:20: note: expanded from macro 'NEQ'
  175 | #define NEQ(a,b) (!EQ(a,b))
      |                    ^
/Users/<USER>/AndroidStudioProjects/UVCCam/libnative/src/main/cpp/module/mp3/lame/machine.h:170:23: note: expanded from macro 'EQ'
  170 |  : (fabs((a)-(b)) <= (fabs(b) * 1e-6f)))
      |                       ^
/Users/<USER>/AndroidStudioProjects/UVCCam/libnative/src/main/cpp/module/mp3/lame/lame.c:1596:13: note: use function 'abs' instead
/Users/<USER>/AndroidStudioProjects/UVCCam/libnative/src/main/cpp/module/mp3/lame/machine.h:175:20: note: expanded from macro 'NEQ'
  175 | #define NEQ(a,b) (!EQ(a,b))
      |                    ^
/Users/<USER>/AndroidStudioProjects/UVCCam/libnative/src/main/cpp/module/mp3/lame/machine.h:170:23: note: expanded from macro 'EQ'
  170 |  : (fabs((a)-(b)) <= (fabs(b) * 1e-6f)))
      |                       ^
4 warnings generated.
[18/27] Building CXX object CMakeFiles/nativelib.dir/proxy/proxy_yuv.cpp.o
[19/27] Building C object CMakeFiles/nativelib.dir/module/mp3/lame/quantize_pvt.c.o
[20/27] Building C object CMakeFiles/nativelib.dir/module/mp3/lame/id3tag.c.o
[21/27] Building C object CMakeFiles/nativelib.dir/module/mp3/lame/bitstream.c.o
[22/27] Building C object CMakeFiles/nativelib.dir/module/mp3/lame/takehiro.c.o
[23/27] Building C object CMakeFiles/nativelib.dir/module/mp3/lame/vbrquantize.c.o
[24/27] Building C object CMakeFiles/nativelib.dir/module/mp3/lame/quantize.c.o
[25/27] Building CXX object CMakeFiles/nativelib.dir/nativelib.cpp.o
[26/27] Building C object CMakeFiles/nativelib.dir/module/mp3/lame/psymodel.c.o
[27/27] Linking CXX shared library /Users/<USER>/AndroidStudioProjects/UVCCam/libnative/build/intermediates/cxx/RelWithDebInfo/4u6a1j5c/obj/arm64-v8a/libnativelib.so
