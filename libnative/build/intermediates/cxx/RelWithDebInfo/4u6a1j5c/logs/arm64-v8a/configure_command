/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/cmake \
  -H/Users/<USER>/AndroidStudioProjects/UVCCam/libnative/src/main/cpp \
  -DCMAKE_SYSTEM_NAME=Android \
  -DCMAKE_EXPORT_COMPILE_COMMANDS=ON \
  -DCMAKE_SYSTEM_VERSION=21 \
  -DANDROID_PLATFORM=android-21 \
  -DANDROID_ABI=arm64-v8a \
  -DCMAKE_ANDROID_ARCH_ABI=arm64-v8a \
  -DANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/27.0.12077973 \
  -DCMAKE_ANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/27.0.12077973 \
  -DCMAKE_TOOLCHAIN_FILE=/Users/<USER>/Library/Android/sdk/ndk/27.0.12077973/build/cmake/android.toolchain.cmake \
  -DCMAKE_MAKE_PROGRAM=/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja \
  -DCMAKE_LIBRARY_OUTPUT_DIRECTORY=/Users/<USER>/AndroidStudioProjects/UVCCam/libnative/build/intermediates/cxx/RelWithDebInfo/4u6a1j5c/obj/arm64-v8a \
  -DCMAKE_RUNTIME_OUTPUT_DIRECTORY=/Users/<USER>/AndroidStudioProjects/UVCCam/libnative/build/intermediates/cxx/RelWithDebInfo/4u6a1j5c/obj/arm64-v8a \
  -DCMAKE_BUILD_TYPE=RelWithDebInfo \
  -B/Users/<USER>/AndroidStudioProjects/UVCCam/libnative/.cxx/RelWithDebInfo/4u6a1j5c/arm64-v8a \
  -GNinja
