[{"level_": 0, "message_": "Start JSON generation. Platform version: 21 min SDK version: arm64-v8a", "file_": "/Users/<USER>/AndroidStudioProjects/UVCCam/libnative/src/main/cpp/CMakeLists.txt", "tag_": "release|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "rebuilding JSON /Users/<USER>/AndroidStudioProjects/UVCCam/libnative/.cxx/RelWithDebInfo/4u6a1j5c/arm64-v8a/android_gradle_build.json due to:", "file_": "/Users/<USER>/AndroidStudioProjects/UVCCam/libnative/src/main/cpp/CMakeLists.txt", "tag_": "release|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "- no fingerprint file, will remove stale configuration folder", "file_": "/Users/<USER>/AndroidStudioProjects/UVCCam/libnative/src/main/cpp/CMakeLists.txt", "tag_": "release|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "removing stale contents from '/Users/<USER>/AndroidStudioProjects/UVCCam/libnative/.cxx/RelWithDebInfo/4u6a1j5c/arm64-v8a'", "file_": "/Users/<USER>/AndroidStudioProjects/UVCCam/libnative/src/main/cpp/CMakeLists.txt", "tag_": "release|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "created folder '/Users/<USER>/AndroidStudioProjects/UVCCam/libnative/.cxx/RelWithDebInfo/4u6a1j5c/arm64-v8a'", "file_": "/Users/<USER>/AndroidStudioProjects/UVCCam/libnative/src/main/cpp/CMakeLists.txt", "tag_": "release|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "executing cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/cmake \\\n  -H/Users/<USER>/AndroidStudioProjects/UVCCam/libnative/src/main/cpp \\\n  -DCMAKE_SYSTEM_NAME=Android \\\n  -DCMAKE_EXPORT_COMPILE_COMMANDS=ON \\\n  -DCMAKE_SYSTEM_VERSION=21 \\\n  -DANDROID_PLATFORM=android-21 \\\n  -DANDROID_ABI=arm64-v8a \\\n  -DCMAKE_ANDROID_ARCH_ABI=arm64-v8a \\\n  -DANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/27.0.12077973 \\\n  -DCMAKE_ANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/27.0.12077973 \\\n  -DCMAKE_TOOLCHAIN_FILE=/Users/<USER>/Library/Android/sdk/ndk/27.0.12077973/build/cmake/android.toolchain.cmake \\\n  -DCMAKE_MAKE_PROGRAM=/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja \\\n  -DCMAKE_LIBRARY_OUTPUT_DIRECTORY=/Users/<USER>/AndroidStudioProjects/UVCCam/libnative/build/intermediates/cxx/RelWithDebInfo/4u6a1j5c/obj/arm64-v8a \\\n  -DCMAKE_RUNTIME_OUTPUT_DIRECTORY=/Users/<USER>/AndroidStudioProjects/UVCCam/libnative/build/intermediates/cxx/RelWithDebInfo/4u6a1j5c/obj/arm64-v8a \\\n  -DCMAKE_BUILD_TYPE=RelWithDebInfo \\\n  -B/Users/<USER>/AndroidStudioProjects/UVCCam/libnative/.cxx/RelWithDebInfo/4u6a1j5c/arm64-v8a \\\n  -GNinja\n", "file_": "/Users/<USER>/AndroidStudioProjects/UVCCam/libnative/src/main/cpp/CMakeLists.txt", "tag_": "release|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/cmake \\\n  -H/Users/<USER>/AndroidStudioProjects/UVCCam/libnative/src/main/cpp \\\n  -DCMAKE_SYSTEM_NAME=Android \\\n  -DCMAKE_EXPORT_COMPILE_COMMANDS=ON \\\n  -DCMAKE_SYSTEM_VERSION=21 \\\n  -DANDROID_PLATFORM=android-21 \\\n  -DANDROID_ABI=arm64-v8a \\\n  -DCMAKE_ANDROID_ARCH_ABI=arm64-v8a \\\n  -DANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/27.0.12077973 \\\n  -DCMAKE_ANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/27.0.12077973 \\\n  -DCMAKE_TOOLCHAIN_FILE=/Users/<USER>/Library/Android/sdk/ndk/27.0.12077973/build/cmake/android.toolchain.cmake \\\n  -DCMAKE_MAKE_PROGRAM=/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja \\\n  -DCMAKE_LIBRARY_OUTPUT_DIRECTORY=/Users/<USER>/AndroidStudioProjects/UVCCam/libnative/build/intermediates/cxx/RelWithDebInfo/4u6a1j5c/obj/arm64-v8a \\\n  -DCMAKE_RUNTIME_OUTPUT_DIRECTORY=/Users/<USER>/AndroidStudioProjects/UVCCam/libnative/build/intermediates/cxx/RelWithDebInfo/4u6a1j5c/obj/arm64-v8a \\\n  -DCMAKE_BUILD_TYPE=RelWithDebInfo \\\n  -B/Users/<USER>/AndroidStudioProjects/UVCCam/libnative/.cxx/RelWithDebInfo/4u6a1j5c/arm64-v8a \\\n  -GNinja\n", "file_": "/Users/<USER>/AndroidStudioProjects/UVCCam/libnative/src/main/cpp/CMakeLists.txt", "tag_": "release|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "Received process result: 0", "file_": "/Users/<USER>/AndroidStudioProjects/UVCCam/libnative/src/main/cpp/CMakeLists.txt", "tag_": "release|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "Exiting generation of /Users/<USER>/AndroidStudioProjects/UVCCam/libnative/.cxx/RelWithDebInfo/4u6a1j5c/arm64-v8a/compile_commands.json.bin normally", "file_": "/Users/<USER>/AndroidStudioProjects/UVCCam/libnative/src/main/cpp/CMakeLists.txt", "tag_": "release|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "done executing cmake", "file_": "/Users/<USER>/AndroidStudioProjects/UVCCam/libnative/src/main/cpp/CMakeLists.txt", "tag_": "release|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "hard linked /Users/<USER>/AndroidStudioProjects/UVCCam/libnative/.cxx/RelWithDebInfo/4u6a1j5c/arm64-v8a/compile_commands.json to /Users/<USER>/AndroidStudioProjects/UVCCam/libnative/.cxx/tools/release/arm64-v8a/compile_commands.json", "file_": "/Users/<USER>/AndroidStudioProjects/UVCCam/libnative/src/main/cpp/CMakeLists.txt", "tag_": "release|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "JSON generation completed without problems", "file_": "/Users/<USER>/AndroidStudioProjects/UVCCam/libnative/src/main/cpp/CMakeLists.txt", "tag_": "release|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}]