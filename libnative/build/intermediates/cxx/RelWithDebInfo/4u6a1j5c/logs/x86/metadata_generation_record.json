[{"level_": 0, "message_": "Start JSON generation. Platform version: 21 min SDK version: x86", "file_": "/Users/<USER>/AndroidStudioProjects/UVCCam/libnative/src/main/cpp/CMakeLists.txt", "tag_": "release|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "JSON '/Users/<USER>/AndroidStudioProjects/UVCCam/libnative/.cxx/RelWithDebInfo/4u6a1j5c/x86/android_gradle_build.json' was up-to-date", "file_": "/Users/<USER>/AndroidStudioProjects/UVCCam/libnative/src/main/cpp/CMakeLists.txt", "tag_": "release|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "JSON generation completed without problems", "file_": "/Users/<USER>/AndroidStudioProjects/UVCCam/libnative/src/main/cpp/CMakeLists.txt", "tag_": "release|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}]