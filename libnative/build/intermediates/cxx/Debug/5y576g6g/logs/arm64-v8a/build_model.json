{"info": {"name": "arm64-v8a", "bitness": 64, "isDefault": true, "isDeprecated": false, "architecture": "arm64", "triple": "aarch64-linux-android", "llvmTriple": "aarch64-none-linux-android"}, "cxxBuildFolder": "/Users/<USER>/AndroidStudioProjects/UVCCam/libnative/.cxx/Debug/5y576g6g/arm64-v8a", "soFolder": "/Users/<USER>/AndroidStudioProjects/UVCCam/libnative/build/intermediates/cxx/Debug/5y576g6g/obj/arm64-v8a", "soRepublishFolder": "/Users/<USER>/AndroidStudioProjects/UVCCam/libnative/build/intermediates/cmake/debug/obj/arm64-v8a", "abiPlatformVersion": 21, "cmake": {"effectiveConfiguration": {"inheritEnvironments": [], "variables": []}}, "variant": {"buildSystemArgumentList": [], "cFlagsList": [], "cppFlagsList": [""], "variantName": "debug", "isDebuggableEnabled": true, "validAbiList": ["arm64-v8a"], "buildTargetSet": [], "implicitBuildTargetSet": [], "cmakeSettingsConfiguration": "android-gradle-plugin-predetermined-name", "module": {"cxxFolder": "/Users/<USER>/AndroidStudioProjects/UVCCam/libnative/.cxx", "intermediatesBaseFolder": "/Users/<USER>/AndroidStudioProjects/UVCCam/libnative/build/intermediates", "intermediatesFolder": "/Users/<USER>/AndroidStudioProjects/UVCCam/libnative/build/intermediates/cxx", "gradleModulePathName": ":libnative", "moduleRootFolder": "/Users/<USER>/AndroidStudioProjects/UVCCam/libnative", "moduleBuildFile": "/Users/<USER>/AndroidStudioProjects/UVCCam/libnative/build.gradle", "makeFile": "/Users/<USER>/AndroidStudioProjects/UVCCam/libnative/src/main/cpp/CMakeLists.txt", "buildSystem": "CMAKE", "ndkFolder": "/Users/<USER>/Library/Android/sdk/ndk/27.0.12077973", "ndkFolderBeforeSymLinking": "/Users/<USER>/Library/Android/sdk/ndk/27.0.12077973", "ndkVersion": "27.0.12077973", "ndkSupportedAbiList": ["armeabi-v7a", "arm64-v8a", "riscv64", "x86", "x86_64"], "ndkDefaultAbiList": ["armeabi-v7a", "arm64-v8a", "x86", "x86_64"], "ndkDefaultStl": "LIBCXX_STATIC", "ndkMetaPlatforms": {"min": 21, "max": 35, "aliases": {"20": 19, "25": 24, "J": 16, "J-MR1": 17, "J-MR2": 18, "K": 19, "L": 21, "L-MR1": 22, "M": 23, "N": 24, "N-MR1": 24, "O": 26, "O-MR1": 27, "P": 28, "Q": 29, "R": 30, "S": 31, "Sv2": 32, "Tiramisu": 33, "UpsideDownCake": 34, "VanillaIceCream": 35}}, "ndkMetaAbiList": [{"name": "armeabi-v7a", "bitness": 32, "isDefault": true, "isDeprecated": false, "architecture": "arm", "triple": "arm-linux-androideabi", "llvmTriple": "armv7-none-linux-androideabi"}, {"name": "arm64-v8a", "bitness": 64, "isDefault": true, "isDeprecated": false, "architecture": "arm64", "triple": "aarch64-linux-android", "llvmTriple": "aarch64-none-linux-android"}, {"name": "riscv64", "bitness": 64, "isDefault": false, "isDeprecated": false, "architecture": "riscv64", "triple": "riscv64-linux-android", "llvmTriple": "riscv64-none-linux-android"}, {"name": "x86", "bitness": 32, "isDefault": true, "isDeprecated": false, "architecture": "x86", "triple": "i686-linux-android", "llvmTriple": "i686-none-linux-android"}, {"name": "x86_64", "bitness": 64, "isDefault": true, "isDeprecated": false, "architecture": "x86_64", "triple": "x86_64-linux-android", "llvmTriple": "x86_64-none-linux-android"}], "cmakeToolchainFile": "/Users/<USER>/Library/Android/sdk/ndk/27.0.12077973/build/cmake/android.toolchain.cmake", "cmake": {"cmakeExe": "/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/cmake"}, "stlSharedObjectMap": {"LIBCXX_SHARED": {"armeabi-v7a": "/Users/<USER>/Library/Android/sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/lib/arm-linux-androideabi/libc++_shared.so", "arm64-v8a": "/Users/<USER>/Library/Android/sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/lib/aarch64-linux-android/libc++_shared.so", "riscv64": "/Users/<USER>/Library/Android/sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/lib/riscv64-linux-android/libc++_shared.so", "x86": "/Users/<USER>/Library/Android/sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/lib/i686-linux-android/libc++_shared.so", "x86_64": "/Users/<USER>/Library/Android/sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/lib/x86_64-linux-android/libc++_shared.so"}, "LIBCXX_STATIC": {}, "NONE": {}, "SYSTEM": {}}, "project": {"rootBuildGradleFolder": "/Users/<USER>/AndroidStudioProjects/UVCCam", "sdkFolder": "/Users/<USER>/Library/Android/sdk", "isBuildOnlyTargetAbiEnabled": true, "ideBuildTargetAbi": "arm64-v8a", "isCmakeBuildCohabitationEnabled": false, "isPrefabEnabled": false}, "outputOptions": [], "ninjaExe": "/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "hasBuildTimeInformation": true}, "prefabClassPaths": [], "prefabPackages": [], "prefabPackageConfigurations": [], "stlType": "c++_static", "optimizationTag": "Debug"}, "buildSettings": {"environmentVariables": []}, "prefabFolder": "/Users/<USER>/AndroidStudioProjects/UVCCam/libnative/.cxx/Debug/5y576g6g/prefab/arm64-v8a", "isActiveAbi": true, "fullConfigurationHash": "5y576g6g15423k3z1ku5u03v4a703e3l6kg1a5454m2o1rn6f4cl1j3i24", "fullConfigurationHashKey": "# Values used to calculate the hash in this folder name.\n# Should not depend on the absolute path of the project itself.\n#   - AGP: 8.11.1.\n#   - $NDK is the path to NDK 27.0.12077973.\n#   - $PROJECT is the path to the parent folder of the root Gradle build file.\n#   - $ABI is the ABI to be built with. The specific value doesn't contribute to the value of the hash.\n#   - $HASH is the hash value computed from this text.\n#   - $CMAKE is the path to CMake 3.22.1.\n#   - $NINJA is the path to Ninja.\n-H$PROJECT/libnative/src/main/cpp\n-DCMAKE_SYSTEM_NAME=Android\n-DCMAKE_EXPORT_COMPILE_COMMANDS=ON\n-DCMAKE_SYSTEM_VERSION=21\n-DANDROID_PLATFORM=android-21\n-DANDROID_ABI=$ABI\n-DCMAKE_ANDROID_ARCH_ABI=$ABI\n-DANDROID_NDK=$NDK\n-DCMAKE_ANDROID_NDK=$NDK\n-DCMAKE_TOOLCHAIN_FILE=$NDK/build/cmake/android.toolchain.cmake\n-DCMAKE_MAKE_PROGRAM=$NINJA\n-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=$PROJECT/libnative/build/intermediates/cxx/Debug/$HASH/obj/$ABI\n-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=$PROJECT/libnative/build/intermediates/cxx/Debug/$HASH/obj/$ABI\n-DCMAKE_BUILD_TYPE=Debug\n-B$PROJECT/libnative/.cxx/Debug/$HASH/$ABI\n-GNinja", "configurationArguments": ["-H/Users/<USER>/AndroidStudioProjects/UVCCam/libnative/src/main/cpp", "-DCMAKE_SYSTEM_NAME=Android", "-DCMAKE_EXPORT_COMPILE_COMMANDS=ON", "-DCMAKE_SYSTEM_VERSION=21", "-DANDROID_PLATFORM=android-21", "-DANDROID_ABI=arm64-v8a", "-DCMAKE_ANDROID_ARCH_ABI=arm64-v8a", "-DANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/27.0.12077973", "-DCMAKE_ANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/27.0.12077973", "-DCMAKE_TOOLCHAIN_FILE=/Users/<USER>/Library/Android/sdk/ndk/27.0.12077973/build/cmake/android.toolchain.cmake", "-DCMAKE_MAKE_PROGRAM=/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=/Users/<USER>/AndroidStudioProjects/UVCCam/libnative/build/intermediates/cxx/Debug/5y576g6g/obj/arm64-v8a", "-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=/Users/<USER>/AndroidStudioProjects/UVCCam/libnative/build/intermediates/cxx/Debug/5y576g6g/obj/arm64-v8a", "-DCMAKE_BUILD_TYPE=Debug", "-B/Users/<USER>/AndroidStudioProjects/UVCCam/libnative/.cxx/Debug/5y576g6g/arm64-v8a", "-<PERSON><PERSON><PERSON><PERSON>"], "intermediatesParentFolder": "/Users/<USER>/AndroidStudioProjects/UVCCam/libnative/build/intermediates/cxx/Debug/5y576g6g"}