[{"level_": 0, "message_": "Start JSON generation. Platform version: 21 min SDK version: armeabi-v7a", "file_": "/Users/<USER>/AndroidStudioProjects/UVCCam/libnative/src/main/cpp/CMakeLists.txt", "tag_": "debug|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "JSON '/Users/<USER>/AndroidStudioProjects/UVCCam/libnative/.cxx/Debug/5y576g6g/armeabi-v7a/android_gradle_build.json' was up-to-date", "file_": "/Users/<USER>/AndroidStudioProjects/UVCCam/libnative/src/main/cpp/CMakeLists.txt", "tag_": "debug|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "JSON generation completed without problems", "file_": "/Users/<USER>/AndroidStudioProjects/UVCCam/libnative/src/main/cpp/CMakeLists.txt", "tag_": "debug|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}]