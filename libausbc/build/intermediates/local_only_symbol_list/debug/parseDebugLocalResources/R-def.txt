R_DEF: Internal format may change without notice
local
attr? insideCircleBgColor
attr? insideCircleTouchedBgColor
attr? insideRectangleBgColor
attr? outsideCircleBgColor
attr? progressArcBgColor
attr? tipTextColor
attr? tipTextSize
color colorBlack
color colorDeepRed
color colorGray
color colorRed
color colorWhite
color common_25_black
color common_30_black
color common_5_black
color common_80_white
color common_E5E5E5
color common_F2F4F6
color common_F5F7FB
color common_a8_black
color common_actionbar_bg_color
color common_actionbar_subtitle_color
color common_actionbar_title_color
color common_activity_bg_color
color common_black
color common_blue_black
color common_blue_light_gray
color common_blue_med_gray
color common_blue_med_light_gray
color common_blue_super_dark_gray
color common_color_accent
color common_color_primary
color common_color_primary_dark
color common_color_primary_disable
color common_color_translucent_gray_bg
color common_dialog_got_it
color common_divider_color
color common_light_blue_gray
color common_orange
color common_red_dot
color common_ripple
color common_shadow_color
color common_white
drawable effect_none
drawable ic_none
drawable imageloader_default_cover_bg
raw base_fragment
raw base_vertex
raw camera_fragment
raw camera_vertex
raw capture_vertex
raw effect_blackw_fragment
raw effect_soul_fragment
raw effect_zoom_vertex
style CommonDialogStyle
style SheetStyle
style TransparentBottomSheetStyle
styleable CircleProgressView outsideCircleBgColor progressArcBgColor insideCircleBgColor insideCircleTouchedBgColor insideRectangleBgColor tipTextColor tipTextSize
xml default_device_filter
