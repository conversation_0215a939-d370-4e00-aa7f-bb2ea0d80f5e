int attr insideCircleBgColor 0x0
int attr insideCircleTouchedBgColor 0x0
int attr insideRectangleBgColor 0x0
int attr outsideCircleBgColor 0x0
int attr progressArcBgColor 0x0
int attr tipTextColor 0x0
int attr tipTextSize 0x0
int color colorBlack 0x0
int color colorDeepRed 0x0
int color colorGray 0x0
int color colorRed 0x0
int color colorWhite 0x0
int color common_25_black 0x0
int color common_30_black 0x0
int color common_5_black 0x0
int color common_80_white 0x0
int color common_E5E5E5 0x0
int color common_F2F4F6 0x0
int color common_F5F7FB 0x0
int color common_a8_black 0x0
int color common_actionbar_bg_color 0x0
int color common_actionbar_subtitle_color 0x0
int color common_actionbar_title_color 0x0
int color common_activity_bg_color 0x0
int color common_black 0x0
int color common_blue_black 0x0
int color common_blue_light_gray 0x0
int color common_blue_med_gray 0x0
int color common_blue_med_light_gray 0x0
int color common_blue_super_dark_gray 0x0
int color common_color_accent 0x0
int color common_color_primary 0x0
int color common_color_primary_dark 0x0
int color common_color_primary_disable 0x0
int color common_color_translucent_gray_bg 0x0
int color common_dialog_got_it 0x0
int color common_divider_color 0x0
int color common_light_blue_gray 0x0
int color common_orange 0x0
int color common_red_dot 0x0
int color common_ripple 0x0
int color common_shadow_color 0x0
int color common_white 0x0
int drawable effect_none 0x0
int drawable ic_none 0x0
int drawable imageloader_default_cover_bg 0x0
int raw base_fragment 0x0
int raw base_vertex 0x0
int raw camera_fragment 0x0
int raw camera_vertex 0x0
int raw capture_vertex 0x0
int raw effect_blackw_fragment 0x0
int raw effect_soul_fragment 0x0
int raw effect_zoom_vertex 0x0
int style CommonDialogStyle 0x0
int style SheetStyle 0x0
int style TransparentBottomSheetStyle 0x0
int[] styleable CircleProgressView { 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
int styleable CircleProgressView_insideCircleBgColor 0
int styleable CircleProgressView_insideCircleTouchedBgColor 1
int styleable CircleProgressView_insideRectangleBgColor 2
int styleable CircleProgressView_outsideCircleBgColor 3
int styleable CircleProgressView_progressArcBgColor 4
int styleable CircleProgressView_tipTextColor 5
int styleable CircleProgressView_tipTextSize 6
int xml default_device_filter 0x0
