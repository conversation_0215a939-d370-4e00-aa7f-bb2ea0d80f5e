<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="colorBlack">#000000</color>
    <color name="colorDeepRed">#B22222</color>
    <color name="colorGray">#CDC5BF</color>
    <color name="colorRed">#FF0000</color>
    <color name="colorWhite">#FFFFFF</color>
    <color name="common_25_black">#40000000</color>
    <color name="common_30_black">#4D000000</color>
    <color name="common_5_black">#13000000</color>
    <color name="common_80_white">#90FFFFFF</color>
    <color name="common_E5E5E5">#FFE5E5E5</color>
    <color name="common_F2F4F6">#F2F4F6</color>
    <color name="common_F5F7FB">#F5F7FB</color>
    <color name="common_a8_black">#A8000000</color>
    <color name="common_actionbar_bg_color">#FFFFFFFF</color>
    <color name="common_actionbar_subtitle_color">#232325</color>
    <color name="common_actionbar_title_color">#232325</color>
    <color name="common_activity_bg_color">#F2F2F2</color>
    <color name="common_black">#000000</color>
    <color name="common_blue_black">#232325</color>
    <color name="common_blue_light_gray">#E9ECF3</color>
    <color name="common_blue_med_gray">#ADB0B7</color>
    <color name="common_blue_med_light_gray">#D7DAE1</color>
    <color name="common_blue_super_dark_gray">#50607A</color>
    <color name="common_color_accent">#2E5BFF</color>
    <color name="common_color_primary">#2E5BFF</color>
    <color name="common_color_primary_dark">#2E5BFF</color>
    <color name="common_color_primary_disable">#D7DAE1</color>
    <color name="common_color_translucent_gray_bg">#70000000</color>
    <color name="common_dialog_got_it">#2E5BFF</color>
    <color name="common_divider_color">#0c000000</color>
    <color name="common_light_blue_gray">#F6F7FB</color>
    <color name="common_orange">#FF6835</color>
    <color name="common_red_dot">#FF371C</color>
    <color name="common_ripple">#f2f5f7</color>
    <color name="common_shadow_color">#88757575</color>
    <color name="common_white">#FFFFFF</color>
    <style name="CommonDialogStyle" parent="@android:style/Theme.Dialog">
        
        <item name="android:windowFrame">@null</item>
        
        <item name="android:windowIsTranslucent">true</item>
        
        <item name="android:windowNoTitle">true</item>
        
        <item name="android:windowBackground">@android:color/transparent</item>
    </style>
    <style name="SheetStyle" parent="android:Widget">
        <item name="android:background">@android:color/transparent</item>
        <item name="behavior_peekHeight">auto</item>
        <item name="behavior_hideable">true</item>
        <item name="behavior_skipCollapsed">false</item>
    </style>
    <style name="TransparentBottomSheetStyle" parent="Theme.Design.Light.BottomSheetDialog">
        <item name="bottomSheetStyle">@style/SheetStyle</item>
    </style>
    <declare-styleable name="CircleProgressView">
        
        <attr format="color|reference" name="outsideCircleBgColor"/>
        
        <attr format="color|reference" name="progressArcBgColor"/>
        
        <attr format="color|reference" name="insideCircleBgColor"/>
        <attr format="color|reference" name="insideCircleTouchedBgColor"/>
        
        <attr format="color|reference" name="insideRectangleBgColor"/>

        
        <attr format="color|reference" name="tipTextColor"/>
        
        <attr format="dimension" name="tipTextSize"/>
    </declare-styleable>
</resources>