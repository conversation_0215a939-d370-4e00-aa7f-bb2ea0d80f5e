/com.jiangdg.ausbc.callback.IPreviewDataCallBackandroid.os.Handler.CallbackBcom.jiangdg.ausbc.encode.H264EncodeProcessor.OnEncodeReadyListener(androidx.appcompat.app.AppCompatActivityandroid.app.ApplicationAcom.google.android.material.bottomsheet.BottomSheetDialogFragment&com.jiangdg.ausbc.base.DialogInterfaceandroidx.fragment.app.Fragment#com.jiangdg.ausbc.base.BaseActivity/com.jiangdg.ausbc.callback.ICameraStateCallBack#com.jiangdg.ausbc.base.BaseFragmentkotlin.Enum(com.jiangdg.ausbc.camera.ICameraStrategy'android.hardware.Camera.PreviewCallback+com.jiangdg.ausbc.MultiCameraClient.ICamera(com.jiangdg.ausbc.camera.bean.CameraInfo*com.jiangdg.ausbc.encode.AbstractProcessor-com.jiangdg.ausbc.encode.audio.IAudioStrategy com.jiangdg.ausbc.pusher.IPusher8android.graphics.SurfaceTexture.OnFrameAvailableListener3com.jiangdg.ausbc.render.internal.AbstractFboRender.com.jiangdg.ausbc.render.effect.AbstractEffect0com.jiangdg.ausbc.render.internal.AbstractRender)java.lang.Thread.UncaughtExceptionHandlerjava.util.concurrent.Future5java.util.concurrent.locks.AbstractQueuedSynchronizer8androidx.recyclerview.widget.RecyclerView.ItemDecoration"androidx.lifecycle.MutableLiveDataandroidx.lifecycle.Observerandroid.opengl.GLSurfaceView%android.opengl.GLSurfaceView.Renderer%com.jiangdg.ausbc.widget.IAspectRatioandroid.view.SurfaceViewandroid.view.TextureViewandroid.view.View,androidx.appcompat.widget.AppCompatImageView+androidx.appcompat.widget.AppCompatTextView                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             