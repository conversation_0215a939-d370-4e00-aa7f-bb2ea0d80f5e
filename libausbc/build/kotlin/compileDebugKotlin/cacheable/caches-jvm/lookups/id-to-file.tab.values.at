/ Header Record For PersistentHashMapValueStorage9 8libausbc/src/main/java/com/jiangdg/ausbc/CameraClient.kt> =libausbc/src/main/java/com/jiangdg/ausbc/MultiCameraClient.kt> =libausbc/src/main/java/com/jiangdg/ausbc/base/BaseActivity.ktA @libausbc/src/main/java/com/jiangdg/ausbc/base/BaseApplication.ktB Alibausbc/src/main/java/com/jiangdg/ausbc/base/BaseBottomDialog.kt< ;libausbc/src/main/java/com/jiangdg/ausbc/base/BaseDialog.kt> =libausbc/src/main/java/com/jiangdg/ausbc/base/BaseFragment.kt@ ?libausbc/src/main/java/com/jiangdg/ausbc/base/CameraActivity.kt@ ?libausbc/src/main/java/com/jiangdg/ausbc/base/CameraFragment.ktA @libausbc/src/main/java/com/jiangdg/ausbc/base/DialogInterface.ktE Dlibausbc/src/main/java/com/jiangdg/ausbc/base/MultiCameraActivity.ktE Dlibausbc/src/main/java/com/jiangdg/ausbc/base/MultiCameraFragment.ktJ Ilibausbc/src/main/java/com/jiangdg/ausbc/callback/ICameraStateCallBack.ktF Elibausbc/src/main/java/com/jiangdg/ausbc/callback/ICaptureCallBack.ktL Klibausbc/src/main/java/com/jiangdg/ausbc/callback/IDeviceConnectCallBack.ktI Hlibausbc/src/main/java/com/jiangdg/ausbc/callback/IEncodeDataCallBack.ktC Blibausbc/src/main/java/com/jiangdg/ausbc/callback/IPlayCallBack.ktJ Ilibausbc/src/main/java/com/jiangdg/ausbc/callback/IPreviewDataCallBack.ktC Blibausbc/src/main/java/com/jiangdg/ausbc/camera/Camera1Strategy.ktC Blibausbc/src/main/java/com/jiangdg/ausbc/camera/Camera2Strategy.kt= <libausbc/src/main/java/com/jiangdg/ausbc/camera/CameraUVC.ktE Dlibausbc/src/main/java/com/jiangdg/ausbc/camera/CameraUvcStrategy.ktC Blibausbc/src/main/java/com/jiangdg/ausbc/camera/ICameraStrategy.ktC Blibausbc/src/main/java/com/jiangdg/ausbc/camera/bean/CameraInfo.ktF Elibausbc/src/main/java/com/jiangdg/ausbc/camera/bean/CameraRequest.ktE Dlibausbc/src/main/java/com/jiangdg/ausbc/camera/bean/CameraStatus.ktF Elibausbc/src/main/java/com/jiangdg/ausbc/camera/bean/CameraUvcInfo.ktE Dlibausbc/src/main/java/com/jiangdg/ausbc/camera/bean/CameraV1Info.ktE Dlibausbc/src/main/java/com/jiangdg/ausbc/camera/bean/CameraV2Info.ktD Clibausbc/src/main/java/com/jiangdg/ausbc/camera/bean/PreviewSize.ktF Elibausbc/src/main/java/com/jiangdg/ausbc/encode/AACEncodeProcessor.ktE Dlibausbc/src/main/java/com/jiangdg/ausbc/encode/AbstractProcessor.ktG Flibausbc/src/main/java/com/jiangdg/ausbc/encode/H264EncodeProcessor.ktM Llibausbc/src/main/java/com/jiangdg/ausbc/encode/audio/AudioStrategySystem.ktJ Ilibausbc/src/main/java/com/jiangdg/ausbc/encode/audio/AudioStrategyUAC.ktH Glibausbc/src/main/java/com/jiangdg/ausbc/encode/audio/IAudioStrategy.kt@ ?libausbc/src/main/java/com/jiangdg/ausbc/encode/bean/RawData.ktB Alibausbc/src/main/java/com/jiangdg/ausbc/encode/muxer/Mp4Muxer.kt? >libausbc/src/main/java/com/jiangdg/ausbc/pusher/AusbcPusher.kt; :libausbc/src/main/java/com/jiangdg/ausbc/pusher/IPusher.ktG Flibausbc/src/main/java/com/jiangdg/ausbc/pusher/aliyun/AliyunPusher.ktK Jlibausbc/src/main/java/com/jiangdg/ausbc/pusher/callback/IStateCallback.ktF Elibausbc/src/main/java/com/jiangdg/ausbc/pusher/config/AusbcConfig.ktA @libausbc/src/main/java/com/jiangdg/ausbc/render/RenderManager.ktI Hlibausbc/src/main/java/com/jiangdg/ausbc/render/effect/AbstractEffect.ktK Jlibausbc/src/main/java/com/jiangdg/ausbc/render/effect/EffectBlackWhite.ktE Dlibausbc/src/main/java/com/jiangdg/ausbc/render/effect/EffectSoul.ktE Dlibausbc/src/main/java/com/jiangdg/ausbc/render/effect/EffectZoom.ktL Klibausbc/src/main/java/com/jiangdg/ausbc/render/effect/bean/CameraEffect.kt> =libausbc/src/main/java/com/jiangdg/ausbc/render/env/EGLEvn.ktB Alibausbc/src/main/java/com/jiangdg/ausbc/render/env/RotateType.ktN Mlibausbc/src/main/java/com/jiangdg/ausbc/render/internal/AbstractFboRender.ktK Jlibausbc/src/main/java/com/jiangdg/ausbc/render/internal/AbstractRender.ktI Hlibausbc/src/main/java/com/jiangdg/ausbc/render/internal/CameraRender.ktJ Ilibausbc/src/main/java/com/jiangdg/ausbc/render/internal/CaptureRender.ktI Hlibausbc/src/main/java/com/jiangdg/ausbc/render/internal/EncodeRender.ktI Hlibausbc/src/main/java/com/jiangdg/ausbc/render/internal/ScreenRender.ktE Dlibausbc/src/main/java/com/jiangdg/ausbc/utils/ActivityStackUtils.kt; :libausbc/src/main/java/com/jiangdg/ausbc/utils/AppUtils.kt> =libausbc/src/main/java/com/jiangdg/ausbc/utils/CameraUtils.kt= <libausbc/src/main/java/com/jiangdg/ausbc/utils/CrashUtils.kt@ ?libausbc/src/main/java/com/jiangdg/ausbc/utils/GLBitmapUtils.kt? >libausbc/src/main/java/com/jiangdg/ausbc/utils/H264TestUtil.kt9 8libausbc/src/main/java/com/jiangdg/ausbc/utils/Logger.kt= <libausbc/src/main/java/com/jiangdg/ausbc/utils/MediaUtils.kt> =libausbc/src/main/java/com/jiangdg/ausbc/utils/OpenGLUtils.ktA @libausbc/src/main/java/com/jiangdg/ausbc/utils/SettableFuture.ktF Elibausbc/src/main/java/com/jiangdg/ausbc/utils/SpaceItemDecoration.kt= <libausbc/src/main/java/com/jiangdg/ausbc/utils/ToastUtils.kt8 7libausbc/src/main/java/com/jiangdg/ausbc/utils/Utils.kt= <libausbc/src/main/java/com/jiangdg/ausbc/utils/bus/BusKey.kt? >libausbc/src/main/java/com/jiangdg/ausbc/utils/bus/EventBus.ktL Klibausbc/src/main/java/com/jiangdg/ausbc/widget/AspectRatioGLSurfaceView.ktJ Ilibausbc/src/main/java/com/jiangdg/ausbc/widget/AspectRatioSurfaceView.ktJ Ilibausbc/src/main/java/com/jiangdg/ausbc/widget/AspectRatioTextureView.ktD Clibausbc/src/main/java/com/jiangdg/ausbc/widget/CaptureMediaView.ktF Elibausbc/src/main/java/com/jiangdg/ausbc/widget/CircleProgressView.kt@ ?libausbc/src/main/java/com/jiangdg/ausbc/widget/IAspectRatio.ktD Clibausbc/src/main/java/com/jiangdg/ausbc/widget/PreviewImageView.kt; :libausbc/src/main/java/com/jiangdg/ausbc/widget/TipView.ktC Blibausbc/src/main/java/com/jiangdg/ausbc/camera/Camera1Strategy.ktC Blibausbc/src/main/java/com/jiangdg/ausbc/camera/Camera2Strategy.ktE Dlibausbc/src/main/java/com/jiangdg/ausbc/camera/CameraUvcStrategy.ktC Blibausbc/src/main/java/com/jiangdg/ausbc/camera/ICameraStrategy.kt> =libausbc/src/main/java/com/jiangdg/ausbc/MultiCameraClient.kt@ ?libausbc/src/main/java/com/jiangdg/ausbc/base/CameraFragment.kt@ ?libausbc/src/main/java/com/jiangdg/ausbc/base/CameraFragment.ktE Dlibausbc/src/main/java/com/jiangdg/ausbc/camera/CameraUvcStrategy.kt9 8libausbc/src/main/java/com/jiangdg/ausbc/CameraClient.kt> =libausbc/src/main/java/com/jiangdg/ausbc/MultiCameraClient.kt@ ?libausbc/src/main/java/com/jiangdg/ausbc/base/CameraActivity.kt@ ?libausbc/src/main/java/com/jiangdg/ausbc/base/CameraFragment.ktE Dlibausbc/src/main/java/com/jiangdg/ausbc/base/MultiCameraActivity.ktE Dlibausbc/src/main/java/com/jiangdg/ausbc/base/MultiCameraFragment.ktL Klibausbc/src/main/java/com/jiangdg/ausbc/callback/IDeviceConnectCallBack.ktE Dlibausbc/src/main/java/com/jiangdg/ausbc/camera/CameraUvcStrategy.ktJ Ilibausbc/src/main/java/com/jiangdg/ausbc/encode/audio/AudioStrategyUAC.kt> =libausbc/src/main/java/com/jiangdg/ausbc/MultiCameraClient.kt@ ?libausbc/src/main/java/com/jiangdg/ausbc/base/CameraActivity.kt@ ?libausbc/src/main/java/com/jiangdg/ausbc/base/CameraFragment.kt9 8libausbc/src/main/java/com/jiangdg/ausbc/CameraClient.kt> =libausbc/src/main/java/com/jiangdg/ausbc/MultiCameraClient.kt@ ?libausbc/src/main/java/com/jiangdg/ausbc/base/CameraActivity.kt@ ?libausbc/src/main/java/com/jiangdg/ausbc/base/CameraFragment.ktE Dlibausbc/src/main/java/com/jiangdg/ausbc/base/MultiCameraActivity.ktE Dlibausbc/src/main/java/com/jiangdg/ausbc/base/MultiCameraFragment.ktL Klibausbc/src/main/java/com/jiangdg/ausbc/callback/IDeviceConnectCallBack.ktE Dlibausbc/src/main/java/com/jiangdg/ausbc/camera/CameraUvcStrategy.ktJ Ilibausbc/src/main/java/com/jiangdg/ausbc/encode/audio/AudioStrategyUAC.kt