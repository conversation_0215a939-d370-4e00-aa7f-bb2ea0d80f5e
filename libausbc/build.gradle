plugins {
    id 'com.android.library'
    id 'kotlin-android'
}

android {
    namespace "com.jiangdg.ausbc"

    compileSdkVersion rootProject.ext.versionCompiler

    defaultConfig {
        minSdkVersion rootProject.ext.minSdkVersion
        targetSdkVersion rootProject.ext.versionTarget
        versionCode rootProject.ext.versionCode
        versionName rootProject.ext.versionNameString

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles "consumer-rules.pro"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    kotlinOptions {
        jvmTarget = '1.8'
    }
    buildFeatures {
        viewBinding = true
    }
}

dependencies {
    implementation "androidx.appcompat:appcompat:${androidXVersion}"
    implementation "androidx.constraintlayout:constraintlayout:${constraintlayoutVersion}"
    implementation "com.google.android.material:material:${materialVersion}"
    api 'com.elvishew:xlog:1.11.0'
//    implementation fileTree("libs")

    implementation project(path: ':libuvc')
    api project(path: ':libnative')
}