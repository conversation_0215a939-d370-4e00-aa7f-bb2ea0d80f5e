package com.example.uvccam

import android.content.Context
import android.content.res.Configuration
import android.hardware.usb.UsbDevice
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.util.DisplayMetrics
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import com.jiangdg.ausbc.MultiCameraClient
import com.jiangdg.ausbc.base.CameraFragment
import com.jiangdg.ausbc.callback.ICameraStateCallBack
import com.jiangdg.ausbc.callback.IPreviewDataCallBack
import com.jiangdg.ausbc.camera.CameraUVC
import com.jiangdg.ausbc.camera.bean.CameraRequest
import com.jiangdg.ausbc.render.env.RotateType
import com.jiangdg.ausbc.widget.AspectRatioTextureView
import com.jiangdg.ausbc.widget.IAspectRatio
import com.example.uvccam.databinding.FragmentUvcCameraBinding

class UvcCameraFragment : CameraFragment() {
    private var _binding: FragmentUvcCameraBinding? = null
    private val binding get() = _binding!!

    // OCR Optimization Constants
    companion object {
        private const val TAG = "UvcCameraFragment"

        // Frame rate optimization for OCR stability (max 15 FPS)
        private const val OCR_OPTIMIZED_MIN_FPS = 5
        private const val OCR_OPTIMIZED_MAX_FPS = 15

        // Bandwidth optimization for stability
        private const val OCR_BANDWIDTH_FACTOR = 0.8f

        // Camera parameter optimization delays
        private const val CAMERA_PARAM_DELAY_MS = 500L
        private const val FOCUS_ADJUSTMENT_DELAY_MS = 1000L
    }

    // Camera optimization state
    private var isOptimizationApplied = false
    private val optimizationHandler = Handler(Looper.getMainLooper())
    private var frameDropCounter = 0
    private var lastFrameTime = 0L
    private var previewDataCallback: IPreviewDataCallBack? = null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentUvcCameraBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        // The parent CameraFragment will handle camera initialization
    }

    /**
     * Allow runtime adjustment of camera rotation if needed
     * Call this method to change rotation without restarting camera
     */
    fun adjustCameraRotation(rotateType: RotateType) {
        setRotateType(rotateType)
    }

    override fun getRootView(inflater: LayoutInflater, container: ViewGroup?): View {
        return onCreateView(inflater, container, null)
    }

    override fun getCameraView(): IAspectRatio? {
        return binding.tvCameraRender
    }

    override fun getCameraViewContainer(): ViewGroup? {
        return binding.container
    }

    override fun generateCamera(ctx: Context, device: UsbDevice): MultiCameraClient.ICamera {
        return CameraUVC(ctx, device)
    }

    override fun getCameraRequest(): CameraRequest {
        // Get optimal resolution based on device screen
        val (previewWidth, previewHeight) = getOptimalPreviewSize()

        return CameraRequest.Builder()
            // Dynamic resolution based on device screen size
            .setPreviewWidth(previewWidth)
            .setPreviewHeight(previewHeight)
            .setRenderMode(CameraRequest.RenderMode.OPENGL)
            // Set 270-degree rotation (90 + 180) to correct orientation issue
            .setDefaultRotateType(RotateType.ANGLE_270)
            .setAudioSource(CameraRequest.AudioSource.SOURCE_SYS_MIC)
            .setPreviewFormat(CameraRequest.PreviewFormat.FORMAT_MJPEG)
            // Disable aspect ratio enforcement to fill entire screen without black borders
            .setAspectRatioShow(false)
            // Enable raw preview data for OCR optimization and frame monitoring
            .setCaptureRawImage(true)
            .setRawPreviewData(true)
            .create()
    }

    /**
     * Get optimal preview size based on device screen resolution
     * Optimized for full-screen usage without black borders on ultra-wide devices like Xiaomi 15
     */
    private fun getOptimalPreviewSize(): Pair<Int, Int> {
        val displayMetrics = DisplayMetrics()
        @Suppress("DEPRECATION")
        requireActivity().windowManager.defaultDisplay.getMetrics(displayMetrics)

        val screenWidth = displayMetrics.widthPixels
        val screenHeight = displayMetrics.heightPixels
        val screenAspectRatio = screenWidth.toFloat() / screenHeight.toFloat()

        // Optimized resolution selection for full-screen coverage without letterboxing
        return when {
            // Ultra-high resolution devices (like Xiaomi 15 - 2670x1200, aspect ratio 2.225:1)
            screenWidth >= 2400 -> {
                if (screenAspectRatio > 2.1f) {
                    // Ultra-wide screens: Try to find resolution closer to screen aspect ratio
                    // Priority: 1920x1080 (1.78:1) - best quality, will stretch to 2.225:1
                    // Alternative: 1280x576 (2.22:1) - closer ratio but may not be supported
                    // Note: With setAspectRatioShow(false), any resolution will stretch to fill screen
                    Pair(1920, 1080) // Will stretch to fill ultra-wide screen without black borders
                } else {
                    // Standard high-res screens (16:9 or similar)
                    Pair(1920, 1080) // Full HD for optimal quality
                }
            }
            // Medium resolution devices
            screenWidth >= 1800 -> Pair(1280, 720)
            // Lower resolution devices
            else -> Pair(640, 480)
        }
    }

    override fun onCameraState(
        self: MultiCameraClient.ICamera,
        code: ICameraStateCallBack.State,
        msg: String?
    ) {
        // Check if fragment is attached to avoid crashes
        if (!isAdded || context == null) {
            return
        }

        try {
            when (code) {
                ICameraStateCallBack.State.OPENED -> {
                    Toast.makeText(requireContext(), "摄像头已开启", Toast.LENGTH_SHORT).show()
                    // Apply OCR optimizations after camera opens
//                    applyOCROptimizations()
                }
                ICameraStateCallBack.State.CLOSED -> {
                    Toast.makeText(requireContext(), "摄像头已关闭", Toast.LENGTH_SHORT).show()
                    // Reset optimization state and clean up callbacks
                    isOptimizationApplied = false
                    optimizationHandler.removeCallbacksAndMessages(null)
                    previewDataCallback?.let { removePreviewDataCallBack(it) }
                    previewDataCallback = null
                }
                ICameraStateCallBack.State.ERROR -> {
                    Toast.makeText(requireContext(), "错误: $msg", Toast.LENGTH_SHORT).show()
                    Log.e(TAG, "Camera error: $msg")
                    // Attempt recovery for certain errors
                    attemptCameraRecovery(msg)
                }
            }
        } catch (e: IllegalStateException) {
            // Fragment not attached to context - ignore the callback
            Log.w(TAG, "Fragment not attached when handling camera state: $code")
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        // Clean up optimization resources
        optimizationHandler.removeCallbacksAndMessages(null)
        previewDataCallback?.let { removePreviewDataCallBack(it) }
        previewDataCallback = null
        isOptimizationApplied = false
        _binding = null
    }

    /**
     * Apply OCR-specific camera optimizations for enhanced text recognition
     * Priority: Preview Stability > Image Clarity > Frame Rate
     */
    private fun applyOCROptimizations() {
        if (isOptimizationApplied) return

        Log.i(TAG, "Applying OCR optimizations...")

        // Phase 1: Preview Stability Optimizations (Highest Priority)
        optimizationHandler.postDelayed({
            applyStabilityOptimizations()
        }, CAMERA_PARAM_DELAY_MS)

        // Phase 2: Image Clarity Optimizations
        optimizationHandler.postDelayed({
            applyImageClarityOptimizations()
        }, CAMERA_PARAM_DELAY_MS + 500)

        // Phase 3: Frame Rate Optimizations
        optimizationHandler.postDelayed({
            applyFrameRateOptimizations()
        }, CAMERA_PARAM_DELAY_MS + 1000)

        // Setup preview data monitoring for stability
        setupPreviewDataMonitoring()

        isOptimizationApplied = true
    }

    /**
     * Phase 1: Apply preview stability optimizations
     */
    private fun applyStabilityOptimizations() {
        try {
            getCurrentCamera()?.let { camera ->
                if (camera is CameraUVC) {
                    Log.i(TAG, "Applying stability optimizations...")

                    // Set optimal frame rate for stability (max 15 FPS)
                    val currentRequest = getCameraRequest()
                    val (width, height) = getOptimalPreviewSize()

                    // Apply frame rate and bandwidth optimizations through native UVC camera
                    // This requires accessing the underlying UVCCamera instance
                    // The frame rate will be controlled through preview data callback
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error applying stability optimizations", e)
        }
    }

    /**
     * Phase 2: Apply image clarity optimizations for OCR
     */
    private fun applyImageClarityOptimizations() {
        try {
            getCurrentCamera()?.let { camera ->
                if (camera is CameraUVC) {
                    Log.i(TAG, "Applying image clarity optimizations...")

                    // Enable auto-focus for sharp text
                    camera.setAutoFocus(true)

                    // Enable auto white balance for better text contrast
                    camera.setAutoWhiteBalance(true)

                    // Optimize camera parameters for text clarity
                    optimizationHandler.postDelayed({
                        optimizeCameraParametersForOCR(camera)
                    }, FOCUS_ADJUSTMENT_DELAY_MS)
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error applying image clarity optimizations", e)
        }
    }

    /**
     * Phase 3: Apply frame rate optimizations
     */
    private fun applyFrameRateOptimizations() {
        try {
            Log.i(TAG, "Applying frame rate optimizations...")
            // Frame rate control will be handled through preview data callback
            // to ensure we don't exceed 15 FPS for stability
        } catch (e: Exception) {
            Log.e(TAG, "Error applying frame rate optimizations", e)
        }
    }

    /**
     * Optimize camera parameters specifically for OCR text recognition
     */
    private fun optimizeCameraParametersForOCR(camera: CameraUVC) {
        try {
            Log.i(TAG, "Optimizing camera parameters for OCR...")

            // Increase sharpness for better text edge definition
            camera.setSharpness(80) // Higher sharpness for text clarity

            // Optimize contrast for better text/background separation
            camera.setContrast(60) // Moderate contrast for text readability

            // Set brightness to neutral for consistent lighting
            camera.setBrightness(50) // Neutral brightness

            // Reduce saturation to focus on text clarity over color
            camera.setSaturation(30) // Lower saturation for text focus

            // Optimize gamma for better text visibility
            camera.setGamma(100) // Standard gamma

            // Set gain to auto for adaptive lighting
            camera.setGain(0) // Auto gain

            Log.i(TAG, "Camera parameters optimized for OCR")
        } catch (e: Exception) {
            Log.e(TAG, "Error optimizing camera parameters for OCR", e)
        }
    }

    /**
     * Setup preview data monitoring for frame rate control and stability
     */
    private fun setupPreviewDataMonitoring() {
        try {
            // Remove existing callback if any
            previewDataCallback?.let { removePreviewDataCallBack(it) }

            // Create the preview data callback for frame rate monitoring
            previewDataCallback = object : IPreviewDataCallBack {
                override fun onPreviewData(
                    data: ByteArray?,
                    width: Int,
                    height: Int,
                    format: IPreviewDataCallBack.DataFormat
                ) {
                    // Implement frame rate control (max 15 FPS)
                    val currentTime = System.currentTimeMillis()
                    val timeDiff = currentTime - lastFrameTime

                    // Only process frames if we're within our target frame rate
                    if (timeDiff >= (1000 / OCR_OPTIMIZED_MAX_FPS)) {
                        lastFrameTime = currentTime
                        frameDropCounter = 0

                        // Frame is within acceptable rate - can be used for OCR
                        // Additional processing can be added here if needed
                        if (frameDropCounter > 0 && frameDropCounter % 100 == 0) {
                            Log.v(TAG, "Frame processed: ${width}x${height}, format: $format")
                        }
                    } else {
                        // Drop frame to maintain target frame rate
                        frameDropCounter++
                        if (frameDropCounter % 30 == 0) { // Log every 30 dropped frames
                            Log.d(TAG, "Dropped $frameDropCounter frames to maintain target FPS")
                        }
                    }
                }
            }

            // Add the preview data callback using the correct method name
            previewDataCallback?.let { addPreviewDataCallBack(it) }
            Log.i(TAG, "Preview data monitoring setup complete")

        } catch (e: Exception) {
            Log.e(TAG, "Error setting up preview data monitoring", e)
        }
    }

    /**
     * Attempt camera recovery for certain error conditions
     */
    private fun attemptCameraRecovery(errorMsg: String?) {
        try {
            Log.i(TAG, "Attempting camera recovery for error: $errorMsg")

            // Common recovery strategies
            when {
                errorMsg?.contains("permission", ignoreCase = true) == true -> {
                    // Permission-related errors - request permission again
                    Log.w(TAG, "Permission error detected, may need to restart camera")
                }
                errorMsg?.contains("device", ignoreCase = true) == true -> {
                    // Device-related errors - try to reconnect
                    optimizationHandler.postDelayed({
                        try {
                            closeCamera()
                            optimizationHandler.postDelayed({
                                openCamera()
                            }, 1000)
                        } catch (e: Exception) {
                            Log.e(TAG, "Error during camera recovery", e)
                        }
                    }, 500)
                }
                else -> {
                    Log.w(TAG, "Unknown error type, no specific recovery action")
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error during camera recovery attempt", e)
        }
    }

    /**
     * Public method to manually trigger OCR optimizations
     * Can be called after camera is opened if automatic optimization fails
     */
    fun manuallyApplyOCROptimizations() {
        if (isCameraOpened()) {
            isOptimizationApplied = false
            applyOCROptimizations()
        } else {
            Log.w(TAG, "Cannot apply optimizations - camera is not opened")
        }
    }

    /**
     * Get current optimization status
     */
    fun isOCROptimizationApplied(): Boolean = isOptimizationApplied

    /**
     * Reset camera parameters to default values
     */
    fun resetCameraParameters() {
        try {
            getCurrentCamera()?.let { camera ->
                if (camera is CameraUVC) {
                    Log.i(TAG, "Resetting camera parameters to defaults...")

                    camera.setSharpness(50)
                    camera.setContrast(50)
                    camera.setBrightness(50)
                    camera.setSaturation(50)
                    camera.setGamma(100)
                    camera.setGain(0)

                    isOptimizationApplied = false
                    Log.i(TAG, "Camera parameters reset to defaults")
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error resetting camera parameters", e)
        }
    }
}