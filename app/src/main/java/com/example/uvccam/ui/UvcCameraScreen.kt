package com.example.uvccam.ui

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.statusBars
import androidx.compose.foundation.layout.windowInsetsPadding
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.viewinterop.AndroidViewBinding
import com.example.uvccam.databinding.CameraHostLayoutBinding // 替换为您的包名

@Composable
fun UvcCameraScreen(modifier: Modifier = Modifier) {
    // Remove title text and use full screen for camera preview
    // Only add status bar padding to avoid overlap with system UI
    Box(
        modifier = modifier
            .fillMaxSize()
            .windowInsetsPadding(WindowInsets.statusBars)
    ) {
        AndroidViewBinding(
            factory = CameraHostLayoutBinding::inflate,
            modifier = Modifier.fillMaxSize()
        ) {
            // 'this' 是 CameraHostLayoutBinding 的一个实例
            // FragmentContainerView 会自动处理Fragment的加载和生命周期
            // 现在相机预览将占据除状态栏外的整个屏幕空间
        }
    }
}