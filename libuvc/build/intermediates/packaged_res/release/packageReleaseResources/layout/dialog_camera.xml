<?xml version="1.0" encoding="utf-8"?>
<!--
  ~  UVCCamera
  ~  library and sample to access to UVC web camera on non-rooted Android device
  ~
  ~ Copyright (c) 2014-2017 saki <EMAIL>
  ~
  ~  Licensed under the Apache License, Version 2.0 (the "License");
  ~  you may not use this file except in compliance with the License.
  ~   You may obtain a copy of the License at
  ~
  ~      http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~   Unless required by applicable law or agreed to in writing, software
  ~   distributed under the License is distributed on an "AS IS" BASIS,
  ~   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~   See the License for the specific language governing permissions and
  ~   limitations under the License.
  ~
  ~  All files in the folder are under this Apache License, Version 2.0.
  ~  Files in the libjpeg-turbo, libusb, libuvc, rapidjson folder
  ~  may have a different license, see the respective files.
  -->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/LinearLayout1"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:paddingBottom="@dimen/vertical_margin"
    android:paddingLeft="@dimen/horizontal_margin"
    android:paddingRight="@dimen/horizontal_margin"
    android:paddingTop="@dimen/vertical_margin"
    tools:context="com.jiangdg.usb.CameraDialog" >

    <TextView
        android:id="@+id/textView1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/camera" />

    <Spinner
        android:id="@+id/spinner1"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />

    <TextView
        android:id="@id/android:empty"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/no_device" />

</LinearLayout>