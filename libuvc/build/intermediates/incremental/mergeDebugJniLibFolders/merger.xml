<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/AndroidStudioProjects/UVCCam/libuvc/src/main/jniLibs"><file name="armeabi-v7a/libUACAudio.so" path="/Users/<USER>/AndroidStudioProjects/UVCCam/libuvc/src/main/jniLibs/armeabi-v7a/libUACAudio.so"/><file name="armeabi-v7a/libuvc.so" path="/Users/<USER>/AndroidStudioProjects/UVCCam/libuvc/src/main/jniLibs/armeabi-v7a/libuvc.so"/><file name="armeabi-v7a/libUVCCamera.so" path="/Users/<USER>/AndroidStudioProjects/UVCCam/libuvc/src/main/jniLibs/armeabi-v7a/libUVCCamera.so"/><file name="armeabi-v7a/libjpeg-turbo1500.so" path="/Users/<USER>/AndroidStudioProjects/UVCCam/libuvc/src/main/jniLibs/armeabi-v7a/libjpeg-turbo1500.so"/><file name="armeabi-v7a/libusb100.so" path="/Users/<USER>/AndroidStudioProjects/UVCCam/libuvc/src/main/jniLibs/armeabi-v7a/libusb100.so"/><file name="x86/libUACAudio.so" path="/Users/<USER>/AndroidStudioProjects/UVCCam/libuvc/src/main/jniLibs/x86/libUACAudio.so"/><file name="x86/libuvc.so" path="/Users/<USER>/AndroidStudioProjects/UVCCam/libuvc/src/main/jniLibs/x86/libuvc.so"/><file name="x86/libUVCCamera.so" path="/Users/<USER>/AndroidStudioProjects/UVCCam/libuvc/src/main/jniLibs/x86/libUVCCamera.so"/><file name="x86/libjpeg-turbo1500.so" path="/Users/<USER>/AndroidStudioProjects/UVCCam/libuvc/src/main/jniLibs/x86/libjpeg-turbo1500.so"/><file name="x86/libusb100.so" path="/Users/<USER>/AndroidStudioProjects/UVCCam/libuvc/src/main/jniLibs/x86/libusb100.so"/><file name="arm64-v8a/libUACAudio.so" path="/Users/<USER>/AndroidStudioProjects/UVCCam/libuvc/src/main/jniLibs/arm64-v8a/libUACAudio.so"/><file name="arm64-v8a/libuvc.so" path="/Users/<USER>/AndroidStudioProjects/UVCCam/libuvc/src/main/jniLibs/arm64-v8a/libuvc.so"/><file name="arm64-v8a/libUVCCamera.so" path="/Users/<USER>/AndroidStudioProjects/UVCCam/libuvc/src/main/jniLibs/arm64-v8a/libUVCCamera.so"/><file name="arm64-v8a/libjpeg-turbo1500.so" path="/Users/<USER>/AndroidStudioProjects/UVCCam/libuvc/src/main/jniLibs/arm64-v8a/libjpeg-turbo1500.so"/><file name="arm64-v8a/libusb100.so" path="/Users/<USER>/AndroidStudioProjects/UVCCam/libuvc/src/main/jniLibs/arm64-v8a/libusb100.so"/><file name="x86_64/libUACAudio.so" path="/Users/<USER>/AndroidStudioProjects/UVCCam/libuvc/src/main/jniLibs/x86_64/libUACAudio.so"/><file name="x86_64/libuvc.so" path="/Users/<USER>/AndroidStudioProjects/UVCCam/libuvc/src/main/jniLibs/x86_64/libuvc.so"/><file name="x86_64/libUVCCamera.so" path="/Users/<USER>/AndroidStudioProjects/UVCCam/libuvc/src/main/jniLibs/x86_64/libUVCCamera.so"/><file name="x86_64/libjpeg-turbo1500.so" path="/Users/<USER>/AndroidStudioProjects/UVCCam/libuvc/src/main/jniLibs/x86_64/libjpeg-turbo1500.so"/><file name="x86_64/libusb100.so" path="/Users/<USER>/AndroidStudioProjects/UVCCam/libuvc/src/main/jniLibs/x86_64/libusb100.so"/></source></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/AndroidStudioProjects/UVCCam/libuvc/src/debug/jniLibs"/></dataSet></merger>