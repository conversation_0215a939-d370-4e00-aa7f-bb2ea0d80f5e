1<?xml version="1.0" encoding="utf-8"?>
2<!--
3  ~  UVCCamera
4  ~  library and sample to access to UVC web camera on non-rooted Android device
5  ~
6  ~ Copyright (c) 2014-2017 saki <EMAIL>
7  ~
8  ~  Licensed under the Apache License, Version 2.0 (the "License");
9  ~  you may not use this file except in compliance with the License.
10  ~   You may obtain a copy of the License at
11  ~
12  ~      http://www.apache.org/licenses/LICENSE-2.0
13  ~
14  ~   Unless required by applicable law or agreed to in writing, software
15  ~   distributed under the License is distributed on an "AS IS" BASIS,
16  ~   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
17  ~   See the License for the specific language governing permissions and
18  ~   limitations under the License.
19  ~
20  ~  All files in the folder are under this Apache License, Version 2.0.
21  ~  Files in the libjpeg-turbo, libusb, libuvc, rapidjson folder
22  ~  may have a different license, see the respective files.
23-->
24<manifest xmlns:android="http://schemas.android.com/apk/res/android"
25    package="com.jiangdg.uvccamera" >
26
27    <uses-sdk android:minSdkVersion="21" />
28
29</manifest>
