EXPORTS
	jcopy_block_row @ 1 ; 
	jcopy_sample_rows @ 2 ; 
	jdiv_round_up @ 3 ; 
	jinit_1pass_quantizer @ 4 ; 
	jinit_2pass_quantizer @ 5 ; 
	jinit_c_coef_controller @ 6 ; 
	jinit_c_main_controller @ 7 ; 
	jinit_c_master_control @ 8 ; 
	jinit_c_prep_controller @ 9 ; 
	jinit_color_converter @ 10 ; 
	jinit_color_deconverter @ 11 ; 
	jinit_compress_master @ 12 ; 
	jinit_d_coef_controller @ 13 ; 
	jinit_d_main_controller @ 14 ; 
	jinit_d_post_controller @ 15 ; 
	jinit_downsampler @ 16 ; 
	jinit_forward_dct @ 17 ; 
	jinit_huff_decoder @ 18 ; 
	jinit_huff_encoder @ 19 ; 
	jinit_input_controller @ 20 ; 
	jinit_inverse_dct @ 21 ; 
	jinit_marker_reader @ 22 ; 
	jinit_marker_writer @ 23 ; 
	jinit_master_decompress @ 24 ; 
	jinit_memory_mgr @ 25 ; 
	jinit_merged_upsampler @ 26 ; 
	jinit_phuff_decoder @ 27 ; 
	jinit_phuff_encoder @ 28 ; 
	jinit_upsampler @ 29 ; 
	jpeg_CreateCompress @ 30 ; 
	jpeg_CreateDecompress @ 31 ; 
	jpeg_abort @ 32 ; 
	jpeg_abort_compress @ 33 ; 
	jpeg_abort_decompress @ 34 ; 
	jpeg_add_quant_table @ 35 ; 
	jpeg_alloc_huff_table @ 36 ; 
	jpeg_alloc_quant_table @ 37 ; 
	jpeg_calc_output_dimensions @ 38 ; 
	jpeg_consume_input @ 39 ; 
	jpeg_copy_critical_parameters @ 40 ; 
	jpeg_default_colorspace @ 41 ; 
	jpeg_destroy @ 42 ; 
	jpeg_destroy_compress @ 43 ; 
	jpeg_destroy_decompress @ 44 ; 
	jpeg_fdct_float @ 45 ; 
	jpeg_fdct_ifast @ 46 ; 
	jpeg_fdct_islow @ 47 ; 
	jpeg_fill_bit_buffer @ 48 ; 
	jpeg_finish_compress @ 49 ; 
	jpeg_finish_decompress @ 50 ; 
	jpeg_finish_output @ 51 ; 
	jpeg_free_large @ 52 ; 
	jpeg_free_small @ 53 ; 
	jpeg_gen_optimal_table @ 54 ; 
	jpeg_get_large @ 55 ; 
	jpeg_get_small @ 56 ; 
	jpeg_has_multiple_scans @ 57 ; 
	jpeg_huff_decode @ 58 ; 
	jpeg_idct_1x1 @ 59 ; 
	jpeg_idct_2x2 @ 60 ; 
	jpeg_idct_4x4 @ 61 ; 
	jpeg_idct_float @ 62 ; 
	jpeg_idct_ifast @ 63 ; 
	jpeg_idct_islow @ 64 ; 
	jpeg_input_complete @ 65 ; 
	jpeg_make_c_derived_tbl @ 66 ; 
	jpeg_make_d_derived_tbl @ 67 ; 
	jpeg_mem_available @ 68 ; 
	jpeg_mem_init @ 69 ; 
	jpeg_mem_term @ 70 ; 
	jpeg_new_colormap @ 71 ; 
	jpeg_open_backing_store @ 72 ; 
	jpeg_quality_scaling @ 73 ; 
	jpeg_read_coefficients @ 74 ; 
	jpeg_read_header @ 75 ; 
	jpeg_read_raw_data @ 76 ; 
	jpeg_read_scanlines @ 77 ; 
	jpeg_resync_to_restart @ 78 ; 
	jpeg_save_markers @ 79 ; 
	jpeg_set_colorspace @ 80 ; 
	jpeg_set_defaults @ 81 ; 
	jpeg_set_linear_quality @ 82 ; 
	jpeg_set_marker_processor @ 83 ; 
	jpeg_set_quality @ 84 ; 
	jpeg_simple_progression @ 85 ; 
	jpeg_start_compress @ 86 ; 
	jpeg_start_decompress @ 87 ; 
	jpeg_start_output @ 88 ; 
	jpeg_std_error @ 89 ; 
	jpeg_stdio_dest @ 90 ; 
	jpeg_stdio_src @ 91 ; 
	jpeg_suppress_tables @ 92 ; 
	jpeg_write_coefficients @ 93 ; 
	jpeg_write_m_byte @ 94 ; 
	jpeg_write_m_header @ 95 ; 
	jpeg_write_marker @ 96 ; 
	jpeg_write_raw_data @ 97 ; 
	jpeg_write_scanlines @ 98 ; 
	jpeg_write_tables @ 99 ; 
	jround_up @ 100 ; 
	jzero_far @ 101 ; 
	jpeg_mem_dest @ 102 ; 
	jpeg_mem_src @ 103 ; 
	jpeg_skip_scanlines @ 104 ; 
	jpeg_crop_scanline @ 105 ; 
