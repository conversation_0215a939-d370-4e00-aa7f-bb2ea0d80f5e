apply plugin: 'com.android.library'

import org.apache.tools.ant.taskdefs.condition.Os

android {
	namespace "com.jiangdg.uvccamera"

	compileSdkVersion rootProject.ext.versionCompiler
	buildToolsVersion rootProject.ext.versionBuildTool

	compileOptions {
		sourceCompatibility rootProject.ext.javaSourceCompatibility
		targetCompatibility rootProject.ext.javaTargetCompatibility
	}

	sourceSets {
		main {
			jni.srcDirs = [] // 關鍵！告訴 Gradle 這裡沒有 JNI 原始碼需要編譯
			jniLibs.srcDirs = ['src/main/jniLibs']
		}
	}

    defaultConfig {
        minSdkVersion rootProject.ext.minSdkVersion
		targetSdkVersion rootProject.ext.versionTarget
    }

	lintOptions {
		checkReleaseBuilds false
		// Or, if you prefer, you can continue to check for errors in release builds,
		// but continue the build even when errors are found:
		abortOnError false
		// The demo app does not have translations.
		disable 'MissingTranslation'
	}

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.txt'
        }
    }
}

//tasks.withType(JavaCompile) {
//	compileTask -> compileTask.dependsOn ndkBuild
//}

//String getNdkBuildPath() {
//	def ndkBuildingDir = System.getenv("NDK_HOME")
//	if (ndkBuildingDir==null || ndkBuildingDir.isEmpty()) {
//		Properties properties = new Properties()
//		properties.load(project.rootProject.file('local.properties').newDataInputStream())
//		ndkBuildingDir = properties.getProperty("ndk.dir")
//	}
//	def ndkBuildPath = ndkBuildingDir
//	if (Os.isFamily(Os.FAMILY_WINDOWS)) {
//		ndkBuildPath = ndkBuildingDir + '/ndk-build.cmd'
//	} else {
//		ndkBuildPath = ndkBuildingDir + '/ndk-build'
//	}
//	return ndkBuildPath
//}

//task ndkBuild(type: Exec, description: 'Compile JNI source via NDK') {
//	println('executing ndkBuild')
//	def ndkBuildPath = getNdkBuildPath();
//	commandLine ndkBuildPath, '-j8', '-C', file('src/main').absolutePath
//}
//
//task ndkClean(type: Exec, description: 'clean JNI libraries') {
//	println('executing ndkBuild clean')
//	def ndkBuildPath = getNdkBuildPath();
//	commandLine ndkBuildPath, 'clean', '-C', file('src/main').absolutePath
//}

//clean.dependsOn 'ndkClean'

dependencies {
	// 保持原有的依賴
	implementation fileTree(dir: new File(buildDir, 'libs'), include: '*.jar')
	implementation "androidx.appcompat:appcompat:${rootProject.ext.androidXVersion}"
	implementation 'com.elvishew:xlog:1.11.0'
}