// Top-level build file where you can add configuration options common to all sub-projects/modules.
plugins {
    alias(libs.plugins.android.application) apply false
    alias(libs.plugins.kotlin.android) apply false
    alias(libs.plugins.kotlin.compose) apply false
}

// 使用 val 定義 Java 版本，方便重用
val javaVersion = JavaVersion.VERSION_1_8

// 这是 Kotlin DSL 中定义全域变数 (extra properties) 的正确方式
// 为所有子模组提供统一的版本配置
extra.apply {
    set("versionCompiler", 34)
    set("versionTarget", 34)
    set("versionMin", 24) // 这是您 app 模组的 minSdk
    set("versionCode", 1)
    set("versionName", "1.0")
    set("javaSourceCompatibility", javaVersion)
    set("javaTargetCompatibility", javaVersion)

    // 2. 为了相容旧模组 Gradle 脚本中使用的特定变数名
    set("minSdkVersion", 21) // 函式库模组需要的最低版本
    set("versionBuildTool", "35.0.0")
    set("versionNameString", "3.3.3")

    // 3. 为了相容 libausbc 模组内部依赖的函式库版本
    set("androidXVersion", "1.3.1")
    set("constraintlayoutVersion", "2.0.4")
    set("materialVersion", "1.3.0")
    set("verAndroidX", "1.2.0")
    set("verAndroidXAppCompat", "1.2.0")
    set("verAndroidXConstraintLayout", "2.0.4")
    set("verAndroidXRecyclerView", "1.2.0")
    set("verMaterial", "1.3.0")
    set("verCoroutines", "1.4.2")
    set("verPermissionX", "1.4.0")
    set("verMMKV", "1.2.9")
    set("verLiveDataBus", "1.7.2")
    set("verGlide", "4.12.0")
    set("verAliPusher", "5.3.1")

    // 4. 提供 Kotlin 版本给旧模组
    // 我们直接从您的 libs.versions.toml 读取版本号，确保版本统一！
    set("kotlin_version", libs.versions.kotlin.get())
}